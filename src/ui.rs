use crate::email_generator::EmailGenerator;
use crate::verification_code::VerificationCodeService;
use eframe::egui;
use std::sync::{Arc, Mutex};
use std::thread;
use std::fs;

const SAVED_EMAILS_FILE: &str = "saved_emails.txt";
const DEFAULT_EMAILS_FILE: &str = "default_emails.txt";

#[derive(Debug, Clone)]
pub struct LogEntry {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub message: String,
    pub level: LogLevel,
}

#[derive(Debug, Clone)]
pub enum LogLevel {
    Info,
    Success,
    Warning,
    Error,
}

pub struct EmailLoadApp {
    email_generator: EmailGenerator,
    verification_service: VerificationCodeService,

    // UI状态
    current_email: String,
    current_verification_code: String,
    is_generating_email: bool,
    is_fetching_code: bool,

    // 邮箱管理
    saved_emails: Vec<String>,
    current_page: usize,
    emails_per_page: usize,

    // 日志
    logs: Vec<LogEntry>,

    // 异步状态
    verification_result: Arc<Mutex<Option<Result<Option<String>, String>>>>,
}

impl EmailLoadApp {
    pub fn new(cc: &eframe::CreationContext<'_>) -> Self {
        // 设置深色主题
        cc.egui_ctx.set_visuals(egui::Visuals::dark());

        // 设置中文字体
        Self::setup_chinese_fonts(&cc.egui_ctx);

        let mut app = Self {
            email_generator: EmailGenerator::new(),
            verification_service: VerificationCodeService::default(),
            current_email: String::new(),
            current_verification_code: String::new(),
            is_generating_email: false,
            is_fetching_code: false,
            saved_emails: Vec::new(),
            current_page: 0,
            emails_per_page: 6,
            logs: Vec::new(),
            verification_result: Arc::new(Mutex::new(None)),
        };

        // 加载保存的邮箱
        app.load_saved_emails();
        app
    }

    fn setup_chinese_fonts(ctx: &egui::Context) {
        let mut fonts = egui::FontDefinitions::default();

        // 加载PingFang中文字体
        fonts.font_data.insert(
            "pingfang".to_owned(),
            egui::FontData::from_static(include_bytes!("../PingFang.ttf")),
        );

        // 将PingFang字体添加到字体族的最前面，确保中文优先使用这个字体
        fonts.families.get_mut(&egui::FontFamily::Proportional)
            .unwrap()
            .insert(0, "pingfang".to_owned());

        fonts.families.get_mut(&egui::FontFamily::Monospace)
            .unwrap()
            .insert(0, "pingfang".to_owned());

        ctx.set_fonts(fonts);
    }

    fn add_log(&mut self, message: String, level: LogLevel) {
        self.logs.push(LogEntry {
            timestamp: chrono::Utc::now(),
            message,
            level,
        });
        
        // 保持日志数量在合理范围内
        if self.logs.len() > 100 {
            self.logs.remove(0);
        }
    }

    fn generate_email(&mut self) {
        self.is_generating_email = true;
        self.current_email = self.email_generator.generate_email();
        self.add_log(
            format!("生成新邮箱: {}", self.current_email),
            LogLevel::Success,
        );
        self.is_generating_email = false;
    }

    fn save_email(&mut self) {
        if !self.current_email.is_empty() && !self.saved_emails.contains(&self.current_email) {
            self.saved_emails.push(self.current_email.clone());
            self.save_emails_to_file();
            self.add_log(
                format!("保存邮箱: {}", self.current_email),
                LogLevel::Success,
            );
        } else if self.saved_emails.contains(&self.current_email) {
            self.add_log(
                "邮箱已存在于保存列表中".to_string(),
                LogLevel::Warning,
            );
        }
    }

    fn load_saved_emails(&mut self) {
        // 首先加载默认邮箱
        self.load_default_emails();

        // 然后加载用户保存的邮箱
        if let Ok(content) = fs::read_to_string(SAVED_EMAILS_FILE) {
            let user_emails: Vec<String> = content
                .lines()
                .filter(|line| !line.trim().is_empty())
                .map(|line| line.trim().to_string())
                .collect();

            // 合并默认邮箱和用户邮箱，去重
            for email in user_emails {
                if !self.saved_emails.contains(&email) {
                    self.saved_emails.push(email);
                }
            }
        }

        if !self.saved_emails.is_empty() {
            self.add_log(
                format!("加载了 {} 个邮箱 (包含默认邮箱)", self.saved_emails.len()),
                LogLevel::Info,
            );
        }
    }

    fn load_default_emails(&mut self) {
        if let Ok(content) = fs::read_to_string(DEFAULT_EMAILS_FILE) {
            self.saved_emails = content
                .lines()
                .filter(|line| !line.trim().is_empty())
                .map(|line| line.trim().to_string())
                .collect();
        }
    }

    fn save_emails_to_file(&self) {
        // 获取默认邮箱列表
        let default_emails = self.get_default_emails();

        // 只保存用户添加的邮箱（排除默认邮箱）
        let user_emails: Vec<String> = self.saved_emails
            .iter()
            .filter(|email| !default_emails.contains(email))
            .cloned()
            .collect();

        let content = user_emails.join("\n");
        if let Err(e) = fs::write(SAVED_EMAILS_FILE, content) {
            eprintln!("保存邮箱到文件失败: {}", e);
        }
    }

    fn get_default_emails(&self) -> Vec<String> {
        if let Ok(content) = fs::read_to_string(DEFAULT_EMAILS_FILE) {
            content
                .lines()
                .filter(|line| !line.trim().is_empty())
                .map(|line| line.trim().to_string())
                .collect()
        } else {
            Vec::new()
        }
    }

    fn copy_to_clipboard(&mut self, text: &str) {
        use clipboard::ClipboardProvider;
        match clipboard::ClipboardContext::new() {
            Ok(mut clipboard) => {
                match clipboard.set_contents(text.to_owned()) {
                    Ok(_) => {
                        self.add_log(
                            format!("已复制到剪贴板: {}", text),
                            LogLevel::Success,
                        );
                    }
                    Err(e) => {
                        self.add_log(
                            format!("复制失败: {}", e),
                            LogLevel::Error,
                        );
                    }
                }
            }
            Err(e) => {
                self.add_log(
                    format!("无法访问剪贴板: {}", e),
                    LogLevel::Error,
                );
            }
        }
    }

    fn fetch_verification_code(&mut self, ctx: &egui::Context) {
        if self.is_fetching_code {
            return;
        }

        self.is_fetching_code = true;
        self.add_log("开始获取验证码...".to_string(), LogLevel::Info);

        // 清除之前的结果
        *self.verification_result.lock().unwrap() = None;

        let verification_service = self.verification_service.clone();
        let result_arc = self.verification_result.clone();
        let ctx_clone = ctx.clone();

        // 在后台线程中获取验证码
        thread::spawn(move || {
            let rt = tokio::runtime::Runtime::new().unwrap();
            let result = rt.block_on(async {
                verification_service.get_verification_code().await
            });

            // 将结果存储到共享状态中
            *result_arc.lock().unwrap() = Some(result);

            // 请求UI重绘
            ctx_clone.request_repaint();
        });
    }

    fn render_header(&mut self, ui: &mut egui::Ui) {
        // 邮箱工作流区域
        self.render_email_workflow(ui);
    }

    fn render_email_workflow(&mut self, ui: &mut egui::Ui) {
        // 使用水平布局分为左右两栏
        ui.horizontal(|ui| {
            // 左侧主要操作区域
            ui.vertical(|ui| {
                ui.set_min_width(450.0);

                // 标题区域
                ui.vertical(|ui| {
                    ui.add_space(15.0);
                    ui.label(
                        egui::RichText::new("📧 邮箱工作流")
                            .size(20.0)
                            .color(egui::Color32::WHITE)
                            .strong(),
                    );

                    ui.label(
                        egui::RichText::new("一键生成邮箱并获取验证码")
                            .size(12.0)
                            .color(egui::Color32::LIGHT_GRAY),
                    );
                    ui.add_space(18.0);
                });

                // 邮箱输入区域
                egui::Frame::none()
                    .fill(egui::Color32::from_gray(40))
                    .rounding(6.0)
                    .inner_margin(12.0)
                    .show(ui, |ui| {
                        ui.label(
                            egui::RichText::new("当前邮箱")
                                .size(12.0)
                                .color(egui::Color32::LIGHT_GRAY),
                        );

                        ui.add_space(6.0);

                        ui.horizontal(|ui| {
                            ui.add_sized(
                                [280.0, 32.0],
                                egui::TextEdit::singleline(&mut self.current_email)
                                    .hint_text("点击生成邮箱按钮生成新邮箱")
                                    .font(egui::TextStyle::Monospace),
                            );

                            if ui.add_sized(
                                [60.0, 32.0],
                                egui::Button::new("📋 复制")
                                    .fill(egui::Color32::from_gray(60))
                            ).clicked() && !self.current_email.is_empty() {
                                let email = self.current_email.clone();
                                self.copy_to_clipboard(&email);
                            }

                            if ui.add_sized(
                                [60.0, 32.0],
                                egui::Button::new("💾 保存")
                                    .fill(egui::Color32::from_rgb(0, 150, 0))
                            ).clicked() && !self.current_email.is_empty() {
                                self.save_email();
                            }
                        });
                    });

                ui.add_space(15.0);

                // 操作按钮区域
                ui.horizontal(|ui| {
                    if ui.add_sized(
                        [140.0, 40.0],
                        egui::Button::new("🎲 生成邮箱")
                            .fill(egui::Color32::from_rgb(138, 43, 226))
                            .rounding(6.0)
                    ).clicked() {
                        self.generate_email();
                    }

                    ui.add_space(12.0);

                    if ui.add_sized(
                        [140.0, 40.0],
                        egui::Button::new("⭐ 获取验证码")
                            .fill(egui::Color32::from_rgb(255, 140, 0))
                            .rounding(6.0)
                    ).clicked() {
                        self.fetch_verification_code(ui.ctx());
                    }
                });

                ui.add_space(15.0);

                // 验证码显示区域
                if !self.current_verification_code.is_empty() {asdas
                    egui::Frame::none()
                        .fill(egui::Color32::from_rgb(0, 60, 0))
                        .rounding(6.0)
                        .inner_margin(12.0)
                        .show(ui, |ui| {
                            ui.label(
                                egui::RichText::new("验证码")
                                    .size(12.0)
                                    .color(egui::Color32::LIGHT_GRAY),
                            );

                            ui.add_space(6.0);

                            ui.horizontal(|ui| {
                                ui.label(
                                    egui::RichText::new(&self.current_verification_code)
                                        .size(20.0)
                                        .color(egui::Color32::from_rgb(0, 255, 0))
                                        .strong()
                                        .family(egui::FontFamily::Monospace),
                                );

                                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                                    if ui.add_sized(
                                        [70.0, 30.0],
                                        egui::Button::new("📋 复制")
                                            .fill(egui::Color32::from_rgb(0, 120, 0))
                                    ).clicked() {
                                        let code = self.current_verification_code.clone();
                                        self.copy_to_clipboard(&code);
                                    }
                                });
                            });
                        });
                }
            });

            ui.add_space(20.0);

            // 右侧邮箱管理区域
            ui.vertical(|ui| {
                ui.set_min_width(300.0);
                ui.set_max_height(400.0);

                if !self.saved_emails.is_empty() {
                    ui.add_space(15.0);

                    // 计算分页信息
                    let total_emails = self.saved_emails.len();
                    let total_pages = (total_emails + self.emails_per_page - 1) / self.emails_per_page;
                    let current_page = self.current_page.min(total_pages.saturating_sub(1));

                    ui.horizontal(|ui| {
                        ui.label(
                            egui::RichText::new("📚 已保存的邮箱")
                                .size(16.0)
                                .color(egui::Color32::WHITE)
                                .strong(),
                        );

                        ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                            if total_pages > 1 {
                                ui.label(
                                    egui::RichText::new(format!("第{}/{}页", current_page + 1, total_pages))
                                        .size(12.0)
                                        .color(egui::Color32::LIGHT_BLUE),
                                );
                                ui.add_space(5.0);
                            }
                            ui.label(
                                egui::RichText::new(format!("({} 个)", total_emails))
                                    .size(12.0)
                                    .color(egui::Color32::GRAY),
                            );
                        });
                    });

                    ui.add_space(12.0);

                    let mut to_remove = None;
                    let mut to_copy = None;
                    let mut to_use = None;

                    // 分页显示邮箱
                    let start_index = current_page * self.emails_per_page;
                    let end_index = (start_index + self.emails_per_page).min(total_emails);
                    let page_emails = &self.saved_emails[start_index..end_index];

                    let default_emails = self.get_default_emails();

                    for (page_index, email) in page_emails.iter().enumerate() {
                        let actual_index = start_index + page_index;
                        let is_default = default_emails.contains(email);
                        let frame_color = if is_default {
                            egui::Color32::from_rgb(40, 40, 60) // 默认邮箱用深蓝色
                        } else {
                            egui::Color32::from_gray(35) // 用户邮箱用灰色
                        };

                        egui::Frame::none()
                            .fill(frame_color)
                            .rounding(4.0)
                            .inner_margin(8.0)
                            .show(ui, |ui| {
                                ui.horizontal(|ui| {
                                    ui.vertical(|ui| {
                                        ui.horizontal(|ui| {
                                            if is_default {
                                                ui.label(
                                                    egui::RichText::new("🔧")
                                                        .size(10.0)
                                                        .color(egui::Color32::LIGHT_BLUE),
                                                );
                                            }
                                            ui.label(
                                                egui::RichText::new(email)
                                                    .size(12.0)
                                                    .color(egui::Color32::WHITE)
                                                    .family(egui::FontFamily::Monospace),
                                            );
                                        });
                                    });

                                    ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                                        // 默认邮箱不能删除
                                        if !is_default && ui.small_button("🗑️").clicked() {
                                            to_remove = Some(actual_index);
                                        }

                                        if ui.small_button("📋").clicked() {
                                            to_copy = Some(email.clone());
                                        }

                                        if ui.small_button("✅").clicked() {
                                            to_use = Some(email.clone());
                                        }
                                    });
                                });
                            });

                        ui.add_space(5.0);
                    }

                    // 分页导航
                    if total_pages > 1 {
                        ui.add_space(10.0);
                        ui.horizontal(|ui| {
                            ui.add_space(5.0);

                            // 上一页按钮
                            if ui.add_enabled(current_page > 0, egui::Button::new("◀ 上一页")).clicked() {
                                self.current_page = current_page.saturating_sub(1);
                            }

                            ui.add_space(10.0);

                            // 页码显示
                            ui.label(
                                egui::RichText::new(format!("{} / {}", current_page + 1, total_pages))
                                    .size(12.0)
                                    .color(egui::Color32::WHITE),
                            );

                            ui.add_space(10.0);

                            // 下一页按钮
                            if ui.add_enabled(current_page < total_pages - 1, egui::Button::new("下一页 ▶")).clicked() {
                                self.current_page = current_page + 1;
                            }
                        });
                        ui.add_space(5.0);
                    }

                    // 处理操作
                    if let Some(index) = to_remove {
                        let removed_email = self.saved_emails.remove(index);
                        self.save_emails_to_file();
                        self.add_log(
                            format!("删除邮箱: {}", removed_email),
                            LogLevel::Info,
                        );

                        // 调整页面，如果当前页没有邮箱了，回到前一页
                        let new_total_pages = (self.saved_emails.len() + self.emails_per_page - 1) / self.emails_per_page;
                        if self.current_page >= new_total_pages && new_total_pages > 0 {
                            self.current_page = new_total_pages - 1;
                        }
                    }

                    if let Some(email) = to_copy {
                        self.copy_to_clipboard(&email);
                    }

                    if let Some(email) = to_use {
                        self.current_email = email.clone();
                        self.add_log(
                            format!("使用已保存的邮箱: {}", email),
                            LogLevel::Info,
                        );
                    }
                }
            });
        });
    }

    fn render_logs(&mut self, ui: &mut egui::Ui) {
        ui.group(|ui| {
            ui.vertical(|ui| {
                ui.horizontal(|ui| {
                    ui.label(
                        egui::RichText::new("操作日志")
                            .size(16.0)
                            .color(egui::Color32::WHITE),
                    );

                    ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                        if ui.button("清除").clicked() {
                            self.logs.clear();
                        }
                    });
                });

                ui.separator();

                egui::ScrollArea::vertical()
                    .max_height(120.0)
                    .auto_shrink([false; 2])
                    .scroll_bar_visibility(egui::scroll_area::ScrollBarVisibility::VisibleWhenNeeded)
                    .stick_to_bottom(true)
                    .show(ui, |ui| {
                        for log in self.logs.iter().rev() {
                            ui.horizontal(|ui| {
                                let time_str = log.timestamp.format("%H:%M:%S").to_string();
                                ui.label(
                                    egui::RichText::new(&time_str)
                                        .size(10.0)
                                        .color(egui::Color32::GRAY),
                                );

                                let (color, prefix) = match log.level {
                                    LogLevel::Info => (egui::Color32::WHITE, "ℹ"),
                                    LogLevel::Success => (egui::Color32::from_rgb(0, 255, 0), "✓"),
                                    LogLevel::Warning => (egui::Color32::from_rgb(255, 165, 0), "⚠"),
                                    LogLevel::Error => (egui::Color32::from_rgb(255, 0, 0), "✗"),
                                };

                                ui.label(
                                    egui::RichText::new(prefix)
                                        .color(color),
                                );

                                ui.label(
                                    egui::RichText::new(&log.message)
                                        .size(12.0)
                                        .color(color),
                                );
                            });
                        }
                    });
            });
        });
    }
}

impl eframe::App for EmailLoadApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // 设置深色主题
        ctx.set_visuals(egui::Visuals::dark());

        // 禁用错误信息显示
        ctx.options_mut(|o| {
            o.warn_on_id_clash = false;
        });

        // 检查验证码获取结果
        let verification_result = if let Ok(mut result_guard) = self.verification_result.try_lock() {
            result_guard.take()
        } else {
            None
        };

        if let Some(result) = verification_result {
            match result {
                Ok(Some(code)) => {
                    self.current_verification_code = code.clone();
                    self.add_log(format!("获取到验证码: {}", code), LogLevel::Success);
                    self.is_fetching_code = false;
                }
                Ok(None) => {
                    self.add_log("未获取到验证码".to_string(), LogLevel::Warning);
                    self.is_fetching_code = false;
                }
                Err(e) => {
                    self.add_log(format!("获取验证码失败: {}", e), LogLevel::Error);
                    self.is_fetching_code = false;
                }
            }
        }

        egui::CentralPanel::default().show(ctx, |ui| {
            ui.add_space(15.0);

            // 主要内容区域
            self.render_header(ui);

            ui.add_space(20.0);

            // 日志区域
            self.render_logs(ui);

            ui.add_space(15.0);
        });
    }
}
